import type { <PERSON>pp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/types";
import { HTTPException } from "hono/http-exception";
import { createErrorResponse } from "@/middlewares/validation";
import { getCurrentUser, getCurrentWorkspace } from "@/middlewares/auth";
import { ListingsService } from "./listings.service";
import type {
  listListingsRoute,
  getListingRoute,
  createListingRoute,
  saveDraftListingRoute,
  updateListingRoute,
  deleteListingRoute,
  bulkCreateListingsRoute,
  bulkCreateListingsCsvRoute,
  getListingStatusHistoryRoute,
} from "./listings.routes";

export const listListings: AppRouteHandler<typeof listListingsRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const workspace = getCurrentWorkspace(c);
    const query = c.req.valid("query");

    const filters = {
      page: query.page,
      limit: query.limit,
      status: query.status,
      industry: query.industry,
      assignedTo: query.assignedTo,
      minPrice: query.minPrice,
      maxPrice: query.maxPrice,
      location: query.location,
      sortBy: query.sortBy,
      sortOrder: query.sortOrder,
      search: query.search,
    };

    const result = await ListingsService.getListings(filters, workspace.id);

    return c.json({
      success: true,
      data: result.data,
      pagination: result.pagination,
    });
  } catch (error) {
    console.error('Error listing listings:', error);
    
    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'LISTING_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }
    
    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to retrieve listings',
      c.req.path
    ), 500);
  }
};

export const getListing: AppRouteHandler<typeof getListingRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const workspace = getCurrentWorkspace(c);
    const { listingId } = c.req.valid("param");
    const { includeDetails } = c.req.valid("query");

    const listing = await ListingsService.getListingById(
      listingId, 
      workspace.id, 
      includeDetails === 'true'
    );

    return c.json({
      success: true,
      data: listing,
    });
  } catch (error) {
    console.error('Error getting listing:', error);
    
    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'LISTING_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }
    
    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to retrieve listing',
      c.req.path
    ), 500);
  }
};

export const createListing: AppRouteHandler<typeof createListingRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const workspace = getCurrentWorkspace(c);
    const body = c.req.valid("json");

    const listingData = {
      ...body,
      workspaceId: workspace.id,
      createdBy: user.id,
    };

    const listing = await ListingsService.createListing(listingData);

    return c.json({
      success: true,
      data: listing,
    }, 201);
  } catch (error) {
    console.error('Error creating listing:', error);
    
    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'CREATION_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }
    
    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to create listing',
      c.req.path
    ), 500);
  }
};

export const saveDraftListing: AppRouteHandler<typeof saveDraftListingRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const workspace = getCurrentWorkspace(c);
    const body = c.req.valid("json");

    const draftData = {
      ...body,
      workspaceId: workspace.id,
      createdBy: user.id,
    };

    const listing = await ListingsService.saveDraftListing(draftData);

    return c.json({
      success: true,
      data: listing,
    }, 201);
  } catch (error) {
    console.error('Error saving draft listing:', error);
    
    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'DRAFT_CREATION_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }
    
    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to save draft listing',
      c.req.path
    ), 500);
  }
};

export const updateListing: AppRouteHandler<typeof updateListingRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const workspace = getCurrentWorkspace(c);
    const { listingId } = c.req.valid("param");
    const body = c.req.valid("json");

    // Check if this is a status update by looking for status field
    const isStatusUpdate = body.status !== undefined;

    let result;
    if (isStatusUpdate) {
      // Handle both listing update and status tracking
      result = await ListingsService.updateListingWithStatus(listingId, body, workspace.id, user.id);
    } else {
      // Handle regular listing update
      result = await ListingsService.updateListing(listingId, body, workspace.id);
    }

    return c.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error updating listing:', error);
    
    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'UPDATE_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }
    
    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to update listing',
      c.req.path
    ), 500);
  }
};

export const deleteListing: AppRouteHandler<typeof deleteListingRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const workspace = getCurrentWorkspace(c);
    const { listingId } = c.req.valid("param");

    await ListingsService.deleteListing(listingId, workspace.id);

    return c.json({
      success: true,
      message: 'Listing deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting listing:', error);
    
    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'DELETE_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }
    
    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to delete listing',
      c.req.path
    ), 500);
  }
};

export const bulkCreateListingsCsv: AppRouteHandler<typeof bulkCreateListingsCsvRoute> = async (c) => {
  try {
    const user = getCurrentUser(c);
    const workspace = getCurrentWorkspace(c);

    // Parse multipart form data
    const body = await c.req.parseBody();
    const file = body.file as File;

    if (!file) {
      return c.json(createErrorResponse(
        'FILE_REQUIRED',
        'CSV file is required',
        c.req.path
      ), 400);
    }

    // Validate file type
    if (!file.type.includes('csv') && !file.name.endsWith('.csv')) {
      return c.json(createErrorResponse(
        'INVALID_FILE_TYPE',
        'File must be a CSV file',
        c.req.path
      ), 400);
    }

    // Validate file size (50MB limit)
    const MAX_FILE_SIZE = 50 * 1024 * 1024;
    if (file.size > MAX_FILE_SIZE) {
      return c.json(createErrorResponse(
        'FILE_TOO_LARGE',
        'File size exceeds maximum limit of 50MB',
        c.req.path
      ), 413);
    }

    // Process CSV in memory and save valid records to database
    const result = await ListingsService.bulkCreateListingsFromCsv(file, workspace.id, user.id);

    return c.json({
      success: true,
      data: result,
    }, 201);
      } catch (error) {
    console.error('Error processing CSV bulk import:', error);

    if (error instanceof HTTPException) {
      return c.json(createErrorResponse(
        'CSV_IMPORT_ERROR',
        error.message,
        c.req.path
      ), error.status);
    }

    return c.json(createErrorResponse(
      'INTERNAL_ERROR',
      'Failed to process CSV import',
      c.req.path
    ), 500);
  }
};

// export const getListingStatusHistory: AppRouteHandler<typeof getListingStatusHistoryRoute> = async (c) => {
//   try {
//     const user = getCurrentUser(c);
//     const workspace = getCurrentWorkspace(c);
//     const { listingId } = c.req.valid("param");

//     const history = await ListingsService.getListingStatusHistory(listingId, workspace.id);

//     return c.json({
//       success: true,
//       data: history,
//     });
//   } catch (error) {
//     console.error('Error getting listing status history:', error);
    
//     if (error instanceof HTTPException) {
//       return c.json(createErrorResponse(
//         'HISTORY_ERROR',
//         error.message,
//         c.req.path
//       ), error.status);
//     }
    
//     return c.json(createErrorResponse(
//       'INTERNAL_ERROR',
//       'Failed to retrieve listing status history',
//       c.req.path
//     ), 500);
//   }
// };
